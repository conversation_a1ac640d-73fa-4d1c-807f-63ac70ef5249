"""
MiniCPM-V-4 视频分析任务
使用MiniCPM-V-4模型进行视频内容分析
"""

import time
import os
import json
from datetime import datetime
from sqlalchemy.orm import Session
from celery import current_task, chain, group
from loguru import logger

from app.core.database import SessionLocal, get_db_session
from app.models.task import Video, AnalysisResult, Task
from app.services.minicpm_v4_service import MiniCPMV4Service
from app.services.task_service import TaskService
from app.services.subtitle_service import SubtitleService
from app.services.ffmpeg_service import ffmpeg_service
from app.services.scene_detection_service import SceneDetectionService
from app.tasks.celery import celery
from app.tasks.task_logger import TaskLogger

from app.tasks.video_tasks import check_task_status
from .video_tasks_basic_info import analyze_video_basic_info
from .video_tasks_content import analyze_video_content
from .video_tasks_plot import analyze_video_plot
from .task_logger import TaskLogger

@celery.task(bind=True)
def analyze_video_with_minicpm_scenes(self, video_id: int, analysis_type: str = "content"):
    """
    使用MiniCPM-V-4分析视频场景内容
    
    Args:
        video_id: 视频ID
        analysis_type: 分析类型 (content, emotion, action, characters, setting)
    """
    task_logger = TaskLogger("MINICPM_SCENE_ANALYSIS", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=3,
            description=f"使用MiniCPM-V-4进行视频场景分析 (类型: {analysis_type})"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        db = SessionLocal()
        minicpm_service = MiniCPMV4Service(db)
        task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
        
        # 步骤2: 执行场景分析
        task_logger.start_step("执行场景分析")
        result = minicpm_service.analyze_video_scenes(video_id, analysis_type)
        task_logger.complete_step(
            "执行场景分析", 
            f"成功分析 {result['total_scenes']} 个场景"
        )
        
        # 步骤3: 保存结果
        task_logger.start_step("保存分析结果")
        # 结果已在服务中保存到数据库
        task_logger.complete_step("保存分析结果", "分析结果已保存到数据库")
            
        task_logger.complete_task(True, f"MiniCPM-V-4场景分析完成，共分析 {result['total_scenes']} 个场景")
        return result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4场景分析失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def detect_scene_changes_with_minicpm(self, video_id: int, comparison_threshold: int = 5):
    """
    使用MiniCPM-V-4检测视频场景变化
    
    Args:
        video_id: 视频ID
        comparison_threshold: 比较阈值（帧数）
    """
    task_logger = TaskLogger("MINICPM_SCENE_CHANGES", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=3,
            description=f"使用MiniCPM-V-4检测场景变化 (阈值: {comparison_threshold})"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 执行场景变化检测
            task_logger.start_step("执行场景变化检测")
            result = minicpm_service.detect_scene_changes(video_id, comparison_threshold)
            task_logger.complete_step(
                "执行场景变化检测", 
                f"完成 {result['total_comparisons']} 次帧比较"
            )
            
            # 步骤3: 保存结果
            task_logger.start_step("保存检测结果")
            # 结果已在服务中保存到数据库
            task_logger.complete_step("保存检测结果", "检测结果已保存到数据库")
            
        task_logger.complete_task(True, f"MiniCPM-V-4场景变化检测完成，共进行 {result['total_comparisons']} 次比较")
        return result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4场景变化检测失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def generate_video_content_summary_with_minicpm(self, video_id: int):
    """
    使用MiniCPM-V-4生成视频内容摘要
    
    Args:
        video_id: 视频ID
    """
    task_logger = TaskLogger("MINICPM_CONTENT_SUMMARY", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=4,
            description="使用MiniCPM-V-4生成视频内容摘要"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 采样关键帧
            task_logger.start_step("采样关键帧")
            # 这一步在服务内部完成
            task_logger.complete_step("采样关键帧", "关键帧采样完成")
            
            # 步骤3: 生成内容摘要
            task_logger.start_step("生成内容摘要")
            result = minicpm_service.analyze_video_content_summary(video_id)
            task_logger.complete_step(
                "生成内容摘要", 
                f"基于 {result['analyzed_frames']} 个关键帧生成摘要"
            )
            
            # 步骤4: 保存结果
            task_logger.start_step("保存摘要结果")
            # 结果已在服务中保存到数据库
            task_logger.complete_step("保存摘要结果", "摘要结果已保存到数据库")
            
        task_logger.complete_task(
            True, 
            f"MiniCPM-V-4内容摘要生成完成，分析了 {result['analyzed_frames']} 个关键帧"
        )
        return result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4内容摘要生成失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def comprehensive_minicpm_analysis(self, video_id: int):
    """
    使用MiniCPM-V-4进行综合视频分析
    包括场景分析、场景变化检测和内容摘要
    
    Args:
        video_id: 视频ID
    """
    task_logger = TaskLogger("MINICPM_COMPREHENSIVE", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=6,
            description="使用MiniCPM-V-4进行综合视频分析"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 内容场景分析
            task_logger.start_step("内容场景分析")
            content_result = minicpm_service.analyze_video_scenes(video_id, "content")
            task_logger.complete_step(
                "内容场景分析", 
                f"分析了 {content_result['total_scenes']} 个场景的内容"
            )
            
            # 步骤3: 情感场景分析
            task_logger.start_step("情感场景分析")
            emotion_result = minicpm_service.analyze_video_scenes(video_id, "emotion")
            task_logger.complete_step(
                "情感场景分析", 
                f"分析了 {emotion_result['total_scenes']} 个场景的情感"
            )
            
            # 步骤4: 场景变化检测
            task_logger.start_step("场景变化检测")
            changes_result = minicpm_service.detect_scene_changes(video_id, 5)
            task_logger.complete_step(
                "场景变化检测", 
                f"完成 {changes_result['total_comparisons']} 次场景变化比较"
            )
            
            # 步骤5: 内容摘要生成
            task_logger.start_step("内容摘要生成")
            summary_result = minicpm_service.analyze_video_content_summary(video_id)
            task_logger.complete_step(
                "内容摘要生成", 
                f"基于 {summary_result['analyzed_frames']} 个关键帧生成摘要"
            )
            
            # 步骤6: 整合结果
            task_logger.start_step("整合分析结果")
            comprehensive_result = {
                "video_id": video_id,
                "content_analysis": content_result,
                "emotion_analysis": emotion_result,
                "scene_changes": changes_result,
                "content_summary": summary_result,
                "analysis_timestamp": minicpm_service._get_current_timestamp()
            }
            task_logger.complete_step("整合分析结果", "所有分析结果已整合")
            
        task_logger.complete_task(True, "MiniCPM-V-4综合视频分析完成")
        return comprehensive_result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4综合分析失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def analyze_specific_frames_with_minicpm(
    self, 
    video_id: int, 
    frame_paths: list, 
    question: str = "Describe what you see in this image in detail."
):
    """
    使用MiniCPM-V-4分析指定的视频帧
    
    Args:
        video_id: 视频ID
        frame_paths: 帧文件路径列表
        question: 分析问题
    """
    task_logger = TaskLogger("MINICPM_SPECIFIC_FRAMES", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=len(frame_paths) + 2,
            description=f"使用MiniCPM-V-4分析 {len(frame_paths)} 个指定帧"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2-N: 分析每个帧
            frame_results = []
            for i, frame_path in enumerate(frame_paths, 1):
                step_name = f"分析帧 {i}/{len(frame_paths)}"
                task_logger.start_step(step_name)
                
                try:
                    result = minicpm_service.analyze_single_frame(video_id, frame_path, question)
                    frame_results.append(result)
                    task_logger.complete_step(step_name, f"帧分析完成: {frame_path}")
                except Exception as e:
                    task_logger.log_warning(f"帧分析失败: {frame_path}", e)
                    task_logger.complete_step(step_name, f"帧分析失败: {str(e)}")
            
            # 最后步骤: 整合结果
            task_logger.start_step("整合分析结果")
            final_result = {
                "video_id": video_id,
                "question": question,
                "total_frames": len(frame_paths),
                "successful_analyses": len(frame_results),
                "frame_analyses": frame_results,
                "analysis_timestamp": minicpm_service._get_current_timestamp()
            }
            task_logger.complete_step("整合分析结果", f"成功分析 {len(frame_results)} 个帧")
            
        task_logger.complete_task(
            True, 
            f"MiniCPM-V-4指定帧分析完成，成功分析 {len(frame_results)}/{len(frame_paths)} 个帧"
        )
        return final_result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4指定帧分析失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise

@celery.task(bind=True)
def analyze_video_scenes_with_subtitles(
    self, 
    video_id: int, 
    analysis_type: str = "content",
    context_window: float = 10.0
):
    """
    使用MiniCPM-V-4分析视频场景，结合字幕信息
    
    Args:
        video_id: 视频ID
        analysis_type: 分析类型
        context_window: 字幕上下文窗口（秒）
    """
    task_logger = TaskLogger("MINICPM_SCENE_SUBTITLES", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=4,
            description=f"使用MiniCPM-V-4进行视频场景分析（含字幕集成，类型: {analysis_type}）"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 检查字幕可用性
            task_logger.start_step("检查字幕可用性")
            subtitle_data = minicpm_service._get_video_subtitles(video_id)
            subtitle_count = len(subtitle_data)
            task_logger.complete_step(
                "检查字幕可用性", 
                f"找到 {subtitle_count} 个字幕文件"
            )
            
            # 步骤3: 执行场景分析（含字幕）
            task_logger.start_step("执行场景分析（含字幕）")
            result = minicpm_service.analyze_video_scenes_with_subtitles(
                video_id, analysis_type, context_window
            )
            task_logger.complete_step(
                "执行场景分析（含字幕）", 
                f"成功分析 {result['total_scenes']} 个场景，字幕集成: {result['subtitle_integration']}"
            )
            
            # 步骤4: 保存结果
            task_logger.start_step("保存分析结果")
            # 结果已在服务中保存到数据库
            task_logger.complete_step("保存分析结果", "分析结果已保存到数据库")
            
        task_logger.complete_task(
            True, 
            f"MiniCPM-V-4场景分析（含字幕）完成，共分析 {result['total_scenes']} 个场景"
        )
        return result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4场景分析（含字幕）失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def generate_video_content_summary_with_subtitles(self, video_id: int):
    """
    使用MiniCPM-V-4生成视频内容摘要，结合字幕信息
    
    Args:
        video_id: 视频ID
    """
    task_logger = TaskLogger("MINICPM_SUMMARY_SUBTITLES", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=5,
            description="使用MiniCPM-V-4生成视频内容摘要（含字幕集成）"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 检查字幕可用性
            task_logger.start_step("检查字幕可用性")
            subtitle_data = minicpm_service._get_video_subtitles(video_id)
            subtitle_count = len(subtitle_data)
            task_logger.complete_step(
                "检查字幕可用性", 
                f"找到 {subtitle_count} 个字幕文件"
            )
            
            # 步骤3: 采样关键帧
            task_logger.start_step("采样关键帧")
            # 这一步在服务内部完成
            task_logger.complete_step("采样关键帧", "关键帧采样完成")
            
            # 步骤4: 生成内容摘要（含字幕）
            task_logger.start_step("生成内容摘要（含字幕）")
            result = minicpm_service.analyze_video_content_summary_with_subtitles(video_id)
            task_logger.complete_step(
                "生成内容摘要（含字幕）", 
                f"基于 {result['analyzed_frames']} 个关键帧和字幕生成摘要"
            )
            
            # 步骤5: 保存结果
            task_logger.start_step("保存摘要结果")
            # 结果已在服务中保存到数据库
            task_logger.complete_step("保存摘要结果", "摘要结果已保存到数据库")
            
        task_logger.complete_task(
            True, 
            f"MiniCPM-V-4内容摘要（含字幕）生成完成，分析了 {result['analyzed_frames']} 个关键帧"
        )
        return result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4内容摘要（含字幕）生成失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def analyze_dialogue_scene_alignment(self, video_id: int):
    """
    分析对话与场景的对齐情况
    
    Args:
        video_id: 视频ID
    """
    task_logger = TaskLogger("MINICPM_DIALOGUE_ALIGNMENT", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=4,
            description="使用MiniCPM-V-4分析对话与场景对齐"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 检查数据可用性
            task_logger.start_step("检查数据可用性")
            subtitle_data = minicpm_service._get_video_subtitles(video_id)
            if not subtitle_data:
                raise ValueError("需要字幕数据进行对话场景对齐分析")
            task_logger.complete_step(
                "检查数据可用性", 
                f"找到 {len(subtitle_data)} 个字幕文件，数据完整"
            )
            
            # 步骤3: 执行对话场景对齐分析
            task_logger.start_step("执行对话场景对齐分析")
            result = minicpm_service.analyze_dialogue_scene_alignment(video_id)
            task_logger.complete_step(
                "执行对话场景对齐分析", 
                f"完成 {result['total_alignments']} 个对话场景对齐分析"
            )
            
            # 步骤4: 保存结果
            task_logger.start_step("保存分析结果")
            # 结果已在服务中保存到数据库
            task_logger.complete_step("保存分析结果", "对齐分析结果已保存到数据库")
            
        task_logger.complete_task(
            True, 
            f"对话场景对齐分析完成，共分析 {result['total_alignments']} 个对齐点"
        )
        return result
        
    except Exception as e:
        task_logger.log_error("对话场景对齐分析失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise


@celery.task(bind=True)
def comprehensive_minicpm_analysis_with_subtitles(self, video_id: int):
    """
    使用MiniCPM-V-4进行综合视频分析（含字幕集成）
    包括场景分析、场景变化检测、内容摘要和对话对齐
    
    Args:
        video_id: 视频ID
    """
    task_logger = TaskLogger("MINICPM_COMPREHENSIVE_SUBTITLES", task_id=self.request.id, video_id=video_id)
    
    try:
        task_logger.start_task(
            total_steps=8,
            description="使用MiniCPM-V-4进行综合视频分析（含字幕集成）"
        )
        
        # 步骤1: 初始化服务
        task_logger.start_step("初始化MiniCPM-V-4服务")
        with get_db_session() as db:
            minicpm_service = MiniCPMV4Service(db)
            task_logger.complete_step("初始化MiniCPM-V-4服务", "服务初始化成功")
            
            # 步骤2: 检查字幕可用性
            task_logger.start_step("检查字幕可用性")
            subtitle_data = minicpm_service._get_video_subtitles(video_id)
            has_subtitles = len(subtitle_data) > 0
            task_logger.complete_step(
                "检查字幕可用性", 
                f"找到 {len(subtitle_data)} 个字幕文件，字幕集成: {'启用' if has_subtitles else '禁用'}"
            )
            
            # 步骤3: 内容场景分析（含字幕）
            task_logger.start_step("内容场景分析（含字幕）")
            if has_subtitles:
                content_result = minicpm_service.analyze_video_scenes_with_subtitles(video_id, "content")
            else:
                content_result = minicpm_service.analyze_video_scenes(video_id, "content")
            task_logger.complete_step(
                "内容场景分析（含字幕）", 
                f"分析了 {content_result['total_scenes']} 个场景的内容"
            )
            
            # 步骤4: 情感场景分析（含字幕）
            task_logger.start_step("情感场景分析（含字幕）")
            if has_subtitles:
                emotion_result = minicpm_service.analyze_video_scenes_with_subtitles(video_id, "emotion")
            else:
                emotion_result = minicpm_service.analyze_video_scenes(video_id, "emotion")
            task_logger.complete_step(
                "情感场景分析（含字幕）", 
                f"分析了 {emotion_result['total_scenes']} 个场景的情感"
            )
            
            # 步骤5: 场景变化检测
            task_logger.start_step("场景变化检测")
            changes_result = minicpm_service.detect_scene_changes(video_id, 5)
            task_logger.complete_step(
                "场景变化检测", 
                f"完成 {changes_result['total_comparisons']} 次场景变化比较"
            )
            
            # 步骤6: 内容摘要生成（含字幕）
            task_logger.start_step("内容摘要生成（含字幕）")
            if has_subtitles:
                summary_result = minicpm_service.analyze_video_content_summary_with_subtitles(video_id)
            else:
                summary_result = minicpm_service.analyze_video_content_summary(video_id)
            task_logger.complete_step(
                "内容摘要生成（含字幕）", 
                f"基于 {summary_result['analyzed_frames']} 个关键帧生成摘要"
            )
            
            # 步骤7: 对话场景对齐分析（仅当有字幕时）
            dialogue_alignment_result = None
            if has_subtitles:
                task_logger.start_step("对话场景对齐分析")
                try:
                    dialogue_alignment_result = minicpm_service.analyze_dialogue_scene_alignment(video_id)
                    task_logger.complete_step(
                        "对话场景对齐分析", 
                        f"完成 {dialogue_alignment_result['total_alignments']} 个对话场景对齐分析"
                    )
                except Exception as e:
                    task_logger.log_warning("对话场景对齐分析失败", e)
                    task_logger.complete_step("对话场景对齐分析", "对话场景对齐分析跳过")
            else:
                task_logger.start_step("对话场景对齐分析")
                task_logger.complete_step("对话场景对齐分析", "无字幕数据，跳过对话场景对齐分析")
            
            # 步骤8: 整合结果
            task_logger.start_step("整合分析结果")
            comprehensive_result = {
                "video_id": video_id,
                "subtitle_integration": has_subtitles,
                "available_subtitles": len(subtitle_data),
                "content_analysis": content_result,
                "emotion_analysis": emotion_result,
                "scene_changes": changes_result,
                "content_summary": summary_result,
                "dialogue_alignment": dialogue_alignment_result,
                "analysis_timestamp": minicpm_service._get_current_timestamp()
            }
            task_logger.complete_step("整合分析结果", "所有分析结果已整合")
            
        task_logger.complete_task(True, "MiniCPM-V-4综合视频分析（含字幕集成）完成")
        return comprehensive_result
        
    except Exception as e:
        task_logger.log_error("MiniCPM-V-4综合分析（含字幕）失败", e)
        task_logger.complete_task(False, f"任务失败: {str(e)}")
        raise



@celery.task(bind=True)
def process_single_video_with_minicpm_analysis(self, video_id: int):
    """处理单个视频的完整分析流程（包含MiniCPM-V-4）"""
    task_logger = TaskLogger("SINGLE_VIDEO_MINICPM", video_id=video_id)
    task_logger.start_task(total_steps=6, description=f"单个视频 {video_id} 完整分析流程（含MiniCPM-V-4）")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取视频信息
        task_logger.start_step("获取视频信息")
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise Exception(f"Video {video_id} not found")
        task_logger.complete_step("获取视频信息", f"视频文件: {video.filename}")

        # 步骤2: 更新视频状态
        task_logger.start_step("更新视频状态")
        video.status = "analyzing"
        db.commit()
        task_logger.complete_step("更新视频状态", "状态已设置为分析中")

        current_task.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 4, 'status': '开始视频分析...'}
        )

        # 步骤3: 创建传统分析任务链
        task_logger.start_step("创建传统分析任务链")
        traditional_analysis_chain = chain(
            analyze_video_basic_info.s(video_id),
            analyze_video_content.si(video_id),
            analyze_video_plot.si(video_id)
        )
        task_logger.complete_step("创建传统分析任务链", "基础信息 -> 内容分析 -> 剧情分析")

        # 步骤4: 启动传统分析链
        task_logger.start_step("启动传统分析链")
        traditional_result = traditional_analysis_chain.apply_async()
        task_logger.log_info(f"传统分析任务已提交到队列: {traditional_result.id}")
        task_logger.complete_step("启动传统分析链", "传统分析任务已启动")

        # 步骤5: 启动MiniCPM-V-4综合分析（优先使用字幕集成版本）
        task_logger.start_step("启动MiniCPM-V-4综合分析")
        minicpm_result = comprehensive_minicpm_analysis_with_subtitles.apply_async(args=[video_id])
        task_logger.log_info(f"MiniCPM-V-4分析任务（含字幕集成）已提交到队列: {minicpm_result.id}")
        task_logger.complete_step("启动MiniCPM-V-4综合分析", "MiniCPM-V-4分析任务已启动")

        # 步骤6: 检查最终结果
        task_logger.start_step("检查最终结果")
        video = db.query(Video).filter(Video.id == video_id).first()
        video.status = "analyzed"
        db.commit()
        
        task_logger.complete_step("检查最终结果", "所有分析步骤完成")
        task_logger.complete_task(True, "视频完整分析完成（含MiniCPM-V-4）")
        
        return {
            'status': 'completed',
            'video_id': video_id,
            'traditional_analysis': traditional_result.successful(),
            'minicpm_analysis': minicpm_result.successful() if 'minicpm_result' in locals() else False,
            'message': '视频完整分析完成（含MiniCPM-V-4）'
        }

    except Exception as e:
        # 更新视频状态为失败
        task_logger.log_error("分析过程中发生异常", e)
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = "failed"
            db.commit()
            task_logger.log_info("视频状态已更新为失败")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"视频分析异常: {str(e)}")
        raise e
    finally:
        db.close()


@celery.task(bind=True)
def process_task_videos_with_minicpm(self, task_id: int):
    """处理任务中的所有视频（包含MiniCPM-V-4分析）"""
    task_logger = TaskLogger("TASK_VIDEOS_MINICPM", task_id=task_id)
    task_logger.start_task(description=f"处理任务 {task_id} 中的所有视频（含MiniCPM-V-4）")
    
    db = SessionLocal()
    try:
        # 步骤1: 获取任务信息
        task_logger.start_step("获取任务信息")
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise Exception(f"Task {task_id} not found")
        task_logger.complete_step("获取任务信息", f"任务名称: {task.name}")

        # 步骤2: 检查任务状态
        task_logger.start_step("检查任务状态")
        if not check_task_status(task_id, db):
            task_logger.log_warning("任务已被暂停或取消")
            task_logger.complete_task(False, "任务已暂停")
            return {
                'status': 'paused',
                'task_id': task_id,
                'message': '任务已暂停'
            }
        task_logger.complete_step("检查任务状态", "任务状态正常")

        # 步骤3: 获取视频列表
        task_logger.start_step("获取视频列表")
        videos = db.query(Video).filter(Video.task_id == task_id).all()
        if not videos:
            raise Exception(f"No videos found for task {task_id}")
        task_logger.complete_step("获取视频列表", f"找到 {len(videos)} 个视频文件")

        # 步骤4: 初始化任务状态
        task_logger.start_step("初始化任务状态")
        task_service = TaskService(db)
        task_service.set_status(task_id, "processing")
        task_service.update_progress(task_id, 0.0)

        current_task.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': len(videos),
                'status': f'开始处理任务中的 {len(videos)} 个视频（含MiniCPM-V-4）...'
            }
        )
        task_logger.complete_step("初始化任务状态", "任务状态已设置为处理中")

        # 步骤5: 创建视频分析任务链（包含MiniCPM-V-4）
        task_logger.start_step("创建视频分析任务链")
        video_tasks = []
        for i, video in enumerate(videos):
            # 更新视频状态
            video.status = "analyzing"
            task_logger.log_info(f"为视频 {video.id} ({video.filename}) 创建完整分析任务链")

            # 创建包含MiniCPM-V-4的完整分析任务
            video_task = process_single_video_with_minicpm_analysis.s(video.id)
            video_tasks.append(video_task)

        db.commit()
        task_logger.complete_step("创建视频分析任务链", f"创建了 {len(video_tasks)} 个完整分析任务")

        # 步骤6: 并行执行分析任务
        task_logger.start_step("并行执行分析任务")
        job = group(video_tasks)
        result = job.apply_async()
        task_logger.log_info("所有视频分析任务已提交到队列")

        # 步骤7: 启动任务监控
        task_logger.start_step("启动任务监控")
        total_videos = len(videos)

        # 记录任务启动信息
        task_service.update_progress(task_id, 10)  # 设置初始进度
        current_task.update_state(
            state='PROGRESS',
            meta={
                'current': 0,
                'total': total_videos,
                'status': f'已启动 {total_videos} 个视频的完整分析任务（含MiniCPM-V-4）'
            }
        )
        task_logger.log_info(f"已启动 {total_videos} 个视频的分析任务，任务ID: {result.id}")
        task_logger.complete_step("启动任务监控", "任务监控已启动，分析将在后台进行")

        # 步骤8: 设置任务状态
        task_logger.start_step("设置任务状态")

        # 设置任务为进行中状态
        task_service.set_status(task_id, "processing")
        task_service.update_progress(task_id, 20.0)  # 任务已启动，设置为20%

        task_logger.complete_step("设置任务状态", "任务状态已设置为处理中")
        task_logger.complete_task(True, f"成功启动 {total_videos} 个视频的完整分析任务（含MiniCPM-V-4）")

        return {
            'status': 'processing',
            'task_id': task_id,
            'videos_to_process': total_videos,
            'message': f'成功启动 {total_videos} 个视频的完整分析任务（含MiniCPM-V-4），分析将在后台进行'
        }

    except Exception as e:
        # 错误处理
        task_logger.log_error("任务执行异常", e)
        task_service = TaskService(db)
        task_service.set_status(task_id, "failed")

        current_task.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        task_logger.complete_task(False, f"任务执行失败: {str(e)}")
        raise e
    finally:
        db.close()